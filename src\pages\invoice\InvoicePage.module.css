.container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background-color: var(--background-color, #ffffff);
  color: var(--text-color, #333333);
}

.container.dark {
  background-color: var(--dark-background, #1a1a1a);
  color: var(--dark-text, #ffffff);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e0e0e0);
}

.dark .header {
  border-bottom-color: var(--dark-border, #333333);
}

.titleSection {
  flex: 1;
}

.title {
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--primary-color, #2563eb);
}

.dark .title {
  color: var(--dark-primary, #60a5fa);
}

.subtitle {
  font-size: 1rem;
  color: var(--text-secondary, #666666);
  margin: 0;
}

.dark .subtitle {
  color: var(--dark-text-secondary, #a0a0a0);
}

.headerActions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.refreshButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: var(--secondary-color, #f3f4f6);
  color: var(--text-color, #333333);
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.refreshButton:hover {
  background-color: var(--secondary-hover, #e5e7eb);
  border-color: var(--border-hover, #9ca3af);
}

.dark .refreshButton {
  background-color: var(--dark-secondary, #374151);
  color: var(--dark-text, #ffffff);
  border-color: var(--dark-border, #4b5563);
}

.dark .refreshButton:hover {
  background-color: var(--dark-secondary-hover, #4b5563);
  border-color: var(--dark-border-hover, #6b7280);
}

.content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.tableSection {
  background-color: var(--card-background, #ffffff);
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark .tableSection {
  background-color: var(--dark-card-background, #2d2d2d);
  border-color: var(--dark-border, #404040);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.tableHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: var(--table-header-bg, #f9fafb);
  border-bottom: 1px solid var(--border-color, #e0e0e0);
}

.dark .tableHeader {
  background-color: var(--dark-table-header-bg, #374151);
  border-bottom-color: var(--dark-border, #404040);
}

.tableInfo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.totalCount {
  font-size: 0.875rem;
  color: var(--text-secondary, #666666);
  font-weight: 500;
}

.dark .totalCount {
  color: var(--dark-text-secondary, #a0a0a0);
}

.tableControls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.pageSizeSelect {
  padding: 0.5rem;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.375rem;
  background-color: var(--input-background, #ffffff);
  color: var(--text-color, #333333);
  font-size: 0.875rem;
  cursor: pointer;
}

.dark .pageSizeSelect {
  background-color: var(--dark-input-background, #374151);
  color: var(--dark-text, #ffffff);
  border-color: var(--dark-border, #4b5563);
}

.errorMessage {
  text-align: center;
  padding: 3rem 2rem;
  background-color: var(--card-background, #ffffff);
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 0.75rem;
  margin: 2rem 0;
}

.dark .errorMessage {
  background-color: var(--dark-card-background, #2d2d2d);
  border-color: var(--dark-border, #404040);
}

.errorMessage h2 {
  color: var(--error-color, #dc2626);
  margin-bottom: 1rem;
}

.dark .errorMessage h2 {
  color: var(--dark-error-color, #f87171);
}

.errorMessage p {
  color: var(--text-secondary, #666666);
  margin-bottom: 1.5rem;
}

.dark .errorMessage p {
  color: var(--dark-text-secondary, #a0a0a0);
}

.retryButton {
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color, #2563eb);
  color: white;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.retryButton:hover {
  background-color: var(--primary-hover, #1d4ed8);
}

.dark .retryButton {
  background-color: var(--dark-primary, #60a5fa);
}

.dark .retryButton:hover {
  background-color: var(--dark-primary-hover, #3b82f6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .headerActions {
    justify-content: flex-end;
  }

  .tableHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .tableControls {
    justify-content: flex-end;
  }

  .title {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.5rem;
  }

  .title {
    font-size: 1.25rem;
  }

  .subtitle {
    font-size: 0.875rem;
  }
}
